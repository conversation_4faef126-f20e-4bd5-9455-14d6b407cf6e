import { auth } from '@/app/(auth)/auth';
import { saveMessages } from '@/lib/db/queries';
import { cookies } from 'next/headers';

export async function POST(request: Request) {
  const session = await auth();
  const cookieStore = await cookies();
  const tempUserId = cookieStore.get('tempUserId')?.value;

  if (!session?.user?.email && !tempUserId) {
    return new Response('Unauthorized', { status: 401 });
  }

  const body = await request.json();
  const { id, chatId, role, content } = body;

  if (!id || !chatId || !role || !content) {
    return new Response('Missing required fields', { status: 400 });
  }

  try {
    // Format the message content based on role
    let formattedContent = content;

    // For assistant messages, format as required
    if (role === 'assistant') {
      formattedContent = [
        {
          type: 'text',
          text: content,
        },
      ];
    }

    // Format the message the same way as text chat
    await saveMessages({
      messages: [
        {
          id,
          chatId,
          role,
          content: formattedContent, // Properly formatted content
          createdAt: new Date(),
          modelId: 'hume-voice', // Custom identifier for Hume voice model
          systemPromptId: null,
          enhancedSystemPrompt: null,
        },
      ],
    });

    return new Response('Message saved', { status: 200 });
  } catch (error) {
    return new Response('Failed to save message', { status: 500 });
  }
}
