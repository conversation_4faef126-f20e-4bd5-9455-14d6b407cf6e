import { motion } from 'framer-motion';

export const Overview = () => {
  return (
    <motion.div
      key="overview"
      className="max-w-3xl mx-auto md:mt-20"
    >
      <div className="border shadow-md rounded gradient-secondary text-zinc-800 p-4 m-2 flex flex-col gap-4 bg-white">
        <h1 className="font-semibold">
          I&apos;m your AI Companion and Life Coach. 💬 ✨
        </h1>
        <p className="leading-normal text-muted-foreground">
          <span className="hidden md:inline">Welcome to GentleGossip!</span>
          <br className="hidden md:block" />
          <br className="hidden md:block" />
          Let&apos;s chat about what&apos;s on your mind. I&apos;ll help you
          find clarity, motivation, and the strength within yourself.
          <br />
          <br />
          Ready to take the next step? What would you like to talk about today?
          🌟
        </p>
      </div>
    </motion.div>
  );
};
